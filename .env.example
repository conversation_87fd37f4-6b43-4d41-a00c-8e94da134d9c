# MinerU Service 环境配置示例
# 复制此文件为 .env 并根据需要修改配置

# ==================== 应用基础配置 ====================
APP_NAME=MinerU Service
APP_VERSION=1.0.0
DEBUG=false
SERVER_HOST=0.0.0.0
SERVER_PORT=3090
SERVER_WORKERS=4

# ==================== MinerU 设备配置 ====================
# 计算设备选择 - cpu: 使用CPU计算(兼容性好但较慢), cuda: 使用GPU加速(需要NVIDIA显卡)
MINERU_DEVICE=cuda
# 显存大小(GB) - 仅在使用GPU时有效，用于优化显存分配和批处理大小
MINERU_VRAM=8
# 语言设置 - ch: 中文优化, en: 英文优化, 影响OCR和文本处理效果
MINERU_LANG=ch

# ==================== MinerU 质量优化参数 ====================
# OCR文字识别置信度阈值 (0.0-1.0) - 低于此值的文字识别结果将被过滤
MINERU_OCR_CONFIDENCE_THRESHOLD=0.7
# 数学公式检测置信度阈值 (0.0-1.0) - 低于此值的公式区域将被忽略，提高可减少误检
MINERU_MFD_CONFIDENCE_THRESHOLD=0.6
# 数学公式识别置信度阈值 (0.0-1.0) - 低于此值的公式识别结果将被过滤
MINERU_MFR_CONFIDENCE_THRESHOLD=0.7
# 页面布局分析置信度阈值 (0.0-1.0) - 低于此值的布局元素将被忽略
MINERU_LAYOUT_CONFIDENCE_THRESHOLD=0.6

# ==================== MinerU 性能优化参数 ====================
# 最小批处理推理大小 - 当页面数量少于此值时不进行批处理优化
MINERU_MIN_BATCH_INFERENCE_SIZE=150

# OCR 批处理大小 - 同时处理的OCR文本块数量，影响文字识别速度和显存占用
MINERU_OCR_BATCH_SIZE=10

# MFR 批处理大小 - 同时处理的数学公式数量 (显存敏感)
# 8GB显存建议4-8, 16GB+显存可用8-16, 过大会导致显存不足
MINERU_MFR_BATCH_SIZE=8

# 布局检测批处理大小 - 同时分析的页面布局数量，影响版面分析速度
MINERU_LAYOUT_BATCH_SIZE=8

# MFD 批处理大小 - 同时检测的数学公式区域数量，影响公式定位速度
MINERU_MFD_BATCH_SIZE=8

# 表格识别批处理大小 - 同时处理的表格数量，表格识别相对耗时建议保持较小值
MINERU_TABLE_BATCH_SIZE=4

# 最大批处理大小 - 所有批处理操作的上限，防止显存溢出
MINERU_MAX_BATCH_SIZE=32

# ==================== MFR 性能优化 ====================
# MFR 最大序列长度 (减少可提高速度，但可能影响复杂公式识别)
MINERU_MFR_MAX_LENGTH=512
# MFR 推理超时时间 (秒)
MINERU_MFR_TIMEOUT=30

# ==================== 并发配置 ====================
# 最大并发请求数 - 同时处理的图片分析请求数量 (模型预加载后可支持更高并发)
# CPU模式建议: 4-8, GPU模式建议: 8-16
MAX_CONCURRENT_REQUESTS=8

# ==================== 日志配置 ====================
LOG_LEVEL=INFO

# ==================== 本地模型配置 ====================
# 是否使用本地模型 (true: 使用本地模型, false: 使用MinerU内置智能下载)
MINERU_USE_LOCAL_MODEL=false
# 本地模型根目录路径 (仅在 MINERU_USE_LOCAL_MODEL=true 时需要)
MINERU_LOCAL_MODEL_PATH=D:\py\models\mineru2.0\PDF-Extract-Kit-1.0

# ==================== Consul配置 ====================
CONSUL_ENABLED=false
CONSUL_HOST=consul.dcx.com
CONSUL_PORT=8500
SERVICE_NAME=mineru-service
SERVICE_HOST="*************"


