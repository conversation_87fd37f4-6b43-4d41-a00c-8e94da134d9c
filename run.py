#!/usr/bin/env python3
"""
MinerU Service 启动入口
"""

import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 确保当前工作目录是项目根目录
os.chdir(project_root)

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

if __name__ == "__main__":
    # 导入并运行主应用
    from m_service.main import main
    main()

