#!/bin/bash

# MinerU 网络问题修复脚本

echo "🌐 MinerU 网络问题诊断和修复"
echo "============================="

# 检查网络连接
echo "🔍 检查网络连接..."

# 测试 HuggingFace 连接
echo "测试 HuggingFace 连接:"
if curl -s --connect-timeout 10 https://huggingface.co > /dev/null; then
    echo "✅ HuggingFace 连接正常"
else
    echo "❌ HuggingFace 连接失败"
    echo "💡 建议启用镜像源"
fi

# 测试镜像源连接
echo ""
echo "测试 HuggingFace 镜像源:"
if curl -s --connect-timeout 10 https://hf-mirror.com > /dev/null; then
    echo "✅ HF 镜像源连接正常"
    MIRROR_AVAILABLE=true
else
    echo "❌ HF 镜像源连接失败"
    MIRROR_AVAILABLE=false
fi

# 检查当前配置
echo ""
echo "📋 当前网络配置:"
if grep -q "^HF_ENDPOINT=" .env; then
    HF_ENDPOINT=$(grep "^HF_ENDPOINT=" .env | cut -d'=' -f2)
    echo "HF_ENDPOINT: $HF_ENDPOINT"
else
    echo "HF_ENDPOINT: 未设置"
fi

RETRIES=$(grep "^MINERU_DOWNLOAD_RETRIES=" .env | cut -d'=' -f2 || echo "3")
TIMEOUT=$(grep "^MINERU_DOWNLOAD_TIMEOUT=" .env | cut -d'=' -f2 || echo "300")
echo "下载重试次数: $RETRIES"
echo "下载超时时间: ${TIMEOUT}s"

# 提供修复选项
echo ""
echo "🔧 修复选项:"
echo "1. 启用 HuggingFace 镜像源 (推荐)"
echo "2. 增加重试次数和超时时间"
echo "3. 重置网络配置为默认值"
echo "4. 查看详细网络诊断"
echo ""
read -p "请选择修复方案 (1-4): " choice

case $choice in
    1)
        echo "🔄 启用 HuggingFace 镜像源..."
        
        # 备份配置
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        
        # 启用镜像源
        if grep -q "^# HF_ENDPOINT=" .env; then
            sed -i 's/^# HF_ENDPOINT=/HF_ENDPOINT=/' .env
        elif grep -q "^HF_ENDPOINT=" .env; then
            sed -i 's/^HF_ENDPOINT=.*/HF_ENDPOINT=https:\/\/hf-mirror.com/' .env
        else
            echo "HF_ENDPOINT=https://hf-mirror.com" >> .env
        fi
        
        echo "✅ 已启用 HuggingFace 镜像源"
        echo "🔄 重启服务以应用配置..."
        docker-compose up -d --force-recreate mineru-service
        ;;
    2)
        echo "🔄 增加重试次数和超时时间..."
        
        # 备份配置
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        
        # 更新重试配置
        sed -i 's/^MINERU_DOWNLOAD_RETRIES=.*/MINERU_DOWNLOAD_RETRIES=5/' .env
        sed -i 's/^MINERU_DOWNLOAD_TIMEOUT=.*/MINERU_DOWNLOAD_TIMEOUT=600/' .env
        
        echo "✅ 已增加重试次数到 5 次，超时时间到 600 秒"
        echo "🔄 重启服务以应用配置..."
        docker-compose up -d --force-recreate mineru-service
        ;;
    3)
        echo "🔄 重置网络配置..."
        
        # 备份配置
        cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
        
        # 重置配置
        sed -i 's/^HF_ENDPOINT=.*/# HF_ENDPOINT=https:\/\/hf-mirror.com/' .env
        sed -i 's/^MINERU_DOWNLOAD_RETRIES=.*/MINERU_DOWNLOAD_RETRIES=3/' .env
        sed -i 's/^MINERU_DOWNLOAD_TIMEOUT=.*/MINERU_DOWNLOAD_TIMEOUT=300/' .env
        
        echo "✅ 已重置网络配置为默认值"
        echo "🔄 重启服务以应用配置..."
        docker-compose up -d --force-recreate mineru-service
        ;;
    4)
        echo "🔍 详细网络诊断..."
        echo ""
        echo "DNS 解析测试:"
        nslookup huggingface.co || echo "DNS 解析失败"
        
        echo ""
        echo "网络延迟测试:"
        ping -c 3 huggingface.co || echo "网络不可达"
        
        echo ""
        echo "代理设置:"
        echo "HTTP_PROXY: ${HTTP_PROXY:-未设置}"
        echo "HTTPS_PROXY: ${HTTPS_PROXY:-未设置}"
        
        echo ""
        echo "Docker 网络:"
        docker network ls | grep mineru
        
        echo ""
        echo "容器网络测试:"
        if docker ps --filter "name=mineru-service" --format "{{.Names}}" | grep -q mineru-service; then
            docker exec mineru-service curl -s --connect-timeout 5 https://huggingface.co > /dev/null && echo "✅ 容器内网络正常" || echo "❌ 容器内网络异常"
        else
            echo "❌ 容器未运行"
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "💡 其他建议:"
echo "- 如果问题持续，考虑使用本地模型 (MINERU_USE_LOCAL_MODEL=true)"
echo "- 检查防火墙和代理设置"
echo "- 确保 Docker 容器有网络访问权限"
echo ""
echo "📋 查看服务日志: docker-compose logs -f mineru-service"
