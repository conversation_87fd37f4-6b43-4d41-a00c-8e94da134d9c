# MinerU Service 管理脚本

## 概述

这些脚本提供了 MinerU Service 的管理和故障排除功能，主要用于 Docker 容器环境。

## 脚本说明

### stop.sh - 停止服务
- 查找并停止所有 uvicorn 进程
- 优雅停止，如果无响应则强制停止
- 支持多进程模式

### status.sh - 查看状态
- 检查服务进程状态（支持多进程）
- 显示端口监听情况
- 显示最近的日志
- 执行健康检查

### update-code.sh - 更新代码
- 快速重启容器加载新代码
- 完全重建容器
- 强制重建镜像
- 适用于代码修改后的更新

### docker-debug.sh - Docker 调试
- 全面的 Docker 服务调试
- 检查容器状态和网络连接
- 显示详细的日志信息
- 端口和健康检查测试

### fix-docker-network.sh - 修复 Docker 网络
- 一键修复 Docker 网络问题
- 重新创建网络和服务
- 自动测试服务连接

### fix-network-issues.sh - 修复网络问题
- 诊断和修复 MinerU 网络连接问题
- 支持启用 HuggingFace 镜像源
- 配置下载重试和超时参数
- 网络连通性测试

## 使用方法

### 1. 启动服务
```bash
# 使用 Docker Compose
docker-compose up -d

# 或使用修复脚本
./scripts/fix-docker-network.sh
```

### 2. 查看状态
```bash
./scripts/status.sh
```

### 3. 更新代码
```bash
./scripts/update-code.sh
```

### 4. 停止服务
```bash
./scripts/stop.sh
# 或
docker-compose down
```

### 5. 调试问题
```bash
# Docker 调试
./scripts/docker-debug.sh

# 网络问题修复
./scripts/fix-network-issues.sh
```

## 环境要求

### Docker 环境
- Docker 和 Docker Compose
- NVIDIA Docker (如果使用 GPU)
- 足够的磁盘空间用于模型和日志

### 系统要求
- Linux/Windows/macOS
- CUDA 环境（如果使用 GPU）
- 网络连接（用于模型下载）

## 配置说明

### 环境变量 (.env 文件)
主要配置项：

- `SERVER_WORKERS`: 工作进程数（默认: 4）
- `MINERU_DEVICE`: 设备类型（cuda/cpu）
- `MINERU_VRAM`: 显存大小（默认: 8GB）
- `HF_ENDPOINT`: HuggingFace 镜像源（可选）
- `CONSUL_ENABLED`: 是否启用 Consul

### 服务配置
- MinerU 内部端口: 8000
- Nginx 对外端口: 3090
- Consul 注册端口: 3090

## 日志管理

### 日志位置
- Docker 日志: `docker-compose logs mineru-service`
- 本地日志: `./logs/mineru-service.log`

### 查看日志
```bash
# 查看实时日志
docker-compose logs -f mineru-service

# 查看最近日志
docker-compose logs --tail 100 mineru-service

# 搜索错误
docker-compose logs mineru-service | grep -i error
```

## 故障排除

### 服务无法启动
1. 运行 `./scripts/docker-debug.sh` 进行诊断
2. 检查 Docker 和 Docker Compose 是否正常
3. 检查端口是否被占用
4. 查看容器日志中的错误信息

### 网络连接问题
1. 运行 `./scripts/fix-network-issues.sh`
2. 启用 HuggingFace 镜像源
3. 检查防火墙和代理设置

### 多进程问题
1. 检查 `SERVER_WORKERS` 配置
2. 考虑降低工作进程数
3. 查看子进程死亡日志

### 性能问题
1. 检查 GPU 使用情况 (`nvidia-smi`)
2. 监控容器资源使用
3. 调整批处理大小参数
4. 考虑调整工作进程数量

## 自定义配置

修改 `.env` 文件中的配置：

```bash
# 基础配置
SERVER_WORKERS=4
SERVER_PORT=3090
MINERU_DEVICE=cuda

# 网络配置
HF_ENDPOINT=https://hf-mirror.com
MINERU_DOWNLOAD_RETRIES=5

# Consul 配置
CONSUL_ENABLED=true
CONSUL_SERVICE_PORT=3090
```
