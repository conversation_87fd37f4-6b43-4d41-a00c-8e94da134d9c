#!/bin/bash

# 简单的容器代码更新脚本

echo "🔄 更新容器中的代码"
echo "==================="

# 检查容器是否运行
if ! docker ps --filter "name=mineru-service" --format "{{.Names}}" | grep -q mineru-service; then
    echo "❌ mineru-service 容器未运行"
    echo "请先启动容器: docker-compose up -d"
    exit 1
fi

echo "📦 容器状态: 运行中"

# 选择更新方式
echo ""
echo "选择更新方式:"
echo "1. 快速重启 (推荐) - 重启容器加载新代码"
echo "2. 完全重建 - 重新创建容器"
echo "3. 强制重建 - 重新构建镜像"
echo ""
read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "🔄 执行快速重启..."
        echo "停止容器..."
        docker-compose stop mineru-service
        
        echo "启动容器..."
        docker-compose up -d mineru-service
        
        echo "✅ 重启完成"
        ;;
    2)
        echo "🏗️  执行完全重建..."
        echo "停止并删除容器..."
        docker-compose down mineru-service
        
        echo "重新创建容器..."
        docker-compose up -d --force-recreate mineru-service
        
        echo "✅ 重建完成"
        ;;
    3)
        echo "🔨 强制重建镜像..."
        echo "停止并删除容器..."
        docker-compose down mineru-service
        
        echo "重新构建镜像..."
        docker-compose build --no-cache mineru-service
        
        echo "启动新容器..."
        docker-compose up -d mineru-service
        
        echo "✅ 强制重建完成"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
echo "🔍 检查服务状态..."
if docker exec mineru-service curl -s -f http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功"
    echo "🌐 访问地址: http://localhost:3090"
    echo "📚 API 文档: http://localhost:3090/docs"
else
    echo "❌ 服务启动失败，查看日志:"
    docker-compose logs --tail 20 mineru-service
fi

echo ""
echo "💡 提示:"
echo "- 查看日志: docker-compose logs -f mineru-service"
echo "- 进入容器: docker exec -it mineru-service bash"
echo "- 检查状态: ./scripts/status.sh"
