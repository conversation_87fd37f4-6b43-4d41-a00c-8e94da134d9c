#!/bin/bash

# 停止 MinerU Service

echo "🛑 停止 MinerU Service..."

# 查找并停止 uvicorn 进程
PIDS=$(ps aux | grep "uvicorn service.main:app" | grep -v grep | awk '{print $2}')

if [ -z "$PIDS" ]; then
    echo "❌ 未找到运行中的 MinerU Service 进程"
    exit 1
fi

echo "📍 找到进程: $PIDS"

for PID in $PIDS; do
    echo "🔄 停止进程 $PID..."
    kill -TERM $PID
    
    # 等待进程优雅退出
    sleep 2
    
    # 检查进程是否还在运行
    if kill -0 $PID 2>/dev/null; then
        echo "⚠️  进程 $PID 未响应 TERM 信号，使用 KILL 信号..."
        kill -KILL $PID
    fi
done

echo "✅ MinerU Service 已停止"
