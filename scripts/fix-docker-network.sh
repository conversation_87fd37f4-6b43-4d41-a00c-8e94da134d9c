#!/bin/bash

# Docker 网络问题修复脚本

echo "🔧 修复 MinerU Docker 网络问题"
echo "================================"

# 停止所有服务
echo "🛑 停止现有服务..."
docker-compose down 2>/dev/null || true

# 清理网络
echo "🧹 清理 Docker 网络..."
docker network prune -f 2>/dev/null || true

# 等待一下
sleep 2

# 重新创建网络和服务
echo "🚀 重新启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 等待 MinerU 服务健康
echo "⏳ 等待 MinerU 服务就绪..."
for i in {1..30}; do
    if docker exec mineru-service curl -s -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ MinerU 服务已就绪"
        break
    fi
    echo "等待中... ($i/30)"
    sleep 5
done

# 测试连接
echo "🔗 测试服务连接..."
echo "测试 MinerU 服务:"
if curl -s -f http://localhost:3090/health > /dev/null 2>&1; then
    echo "✅ MinerU 服务正常"
else
    echo "❌ MinerU 服务失败"
fi

echo ""
echo "🎉 修复完成！"
echo "📍 服务地址:"
echo "   - 主要访问: http://localhost:3090"
echo "   - API 文档: http://localhost:3090/docs"
echo "   - Consul 注册端口: 3090"
