#!/bin/bash

# 检查 MinerU Service 状态

echo "🔍 检查 MinerU Service 状态..."

# 查找 uvicorn 进程
PIDS=$(ps aux | grep "uvicorn service.main:app" | grep -v grep | awk '{print $2}')

if [ -z "$PIDS" ]; then
    echo "❌ MinerU Service 未运行"
    exit 1
fi

# 统计进程数量
PROCESS_COUNT=$(echo "$PIDS" | wc -w)
echo "✅ MinerU Service 正在运行"
echo "📍 进程数量: $PROCESS_COUNT"
echo "📍 进程PIDs: $PIDS"

# 显示进程详情
echo ""
echo "📊 进程详情:"
ps aux | grep "uvicorn service.main:app" | grep -v grep | while read line; do
    echo "   $line"
done

# 检查端口 (支持多个可能的端口)
echo ""
echo "🌐 端口状态:"
for port in 3090 8000; do
    if netstat -tlnp 2>/dev/null | grep ":$port " > /dev/null; then
        echo "✅ 端口 $port 正在监听"
        netstat -tlnp 2>/dev/null | grep ":$port "
    fi
done

# 检查多个可能的日志文件
echo ""
echo "📝 日志文件:"
LOG_FILES=("./logs/mineru-service.log" "./logs/run.log")
FOUND_LOG=false

for log_file in "${LOG_FILES[@]}"; do
    if [ -f "$log_file" ]; then
        echo "📄 日志文件: $log_file"
        echo "📊 文件大小: $(du -h "$log_file" | cut -f1)"
        echo "🕒 最后修改: $(stat -c %y "$log_file" 2>/dev/null || stat -f %Sm "$log_file" 2>/dev/null)"
        echo "📋 最后10行日志:"
        tail -10 "$log_file" | sed 's/^/   /'
        FOUND_LOG=true
        break
    fi
done

if [ "$FOUND_LOG" = false ]; then
    echo "❌ 未找到日志文件"
fi

# 检查服务健康状态
echo ""
echo "🏥 健康检查:"
for port in 3090 8000; do
    if curl -s -f "http://localhost:$port/health" > /dev/null 2>&1; then
        echo "✅ 健康检查通过 (端口 $port)"
        curl -s "http://localhost:$port/health" | head -3
        break
    fi
done

# 测试服务健康状态
echo ""
echo "🏥 健康检查:"
if command -v curl >/dev/null 2>&1; then
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3090/health)
    if [ "$HTTP_CODE" = "200" ]; then
        echo "✅ 服务健康检查通过"
    else
        echo "❌ 服务健康检查失败 (HTTP $HTTP_CODE)"
    fi
else
    echo "⚠️  curl 未安装，跳过健康检查"
fi
