# MinerU Service Dockerfile

FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 配置 Debian APT 源为国内镜像
RUN echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm main contrib non-free non-free-firmware" > /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian/ bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    wget \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgcc-s1 \
    libfontconfig1 \
    libxss1 \
    libgconf-2-4 \
    libxtst6 \
    libxrandr2 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# 配置 pip 镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 升级 pip
RUN pip install --upgrade pip

# 安装 uv
RUN pip install uv

# 使用 uv 安装 mineru[core] (配置镜像源加速)
# RUN uv pip install --system -U "mineru[core]==2.1.0" --index-url https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn


# 复制依赖文件
 COPY pyproject.toml .
 COPY uv.lock .

# 安装 Python 依赖
#RUN pip install --no-cache-dir -r requirements.txt
RUN uv sync --frozen --no-cache --system --index https://pypi.tuna.tsinghua.edu.cn/simple

# 复制应用代码
COPY m_service/ ./m_service/
COPY scripts/ ./scripts/
COPY run.py ./

# 设置环境变量
ENV PYTHONPATH=/app:${PYTHONPATH:-}

# 创建必要的目录
RUN mkdir -p /app/models /app/cache /app/logs /app/data/tmp

# 设置权限
RUN chmod -R 755 /app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "m_service/main.py"]
