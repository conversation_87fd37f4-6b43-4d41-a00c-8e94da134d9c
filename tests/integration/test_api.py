"""
API集成测试
"""

import pytest
from fastapi.testclient import TestClient
from PIL import Image
import io


def test_health_endpoint(client):
    """测试健康检查接口"""
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert "version" in data
    assert "timestamp" in data


def test_ocr_endpoint_with_valid_image(client, sample_image):
    """测试OCR接口（有效图像）"""
    files = {"image": ("test.png", sample_image, "image/png")}
    data = {"enable_formula_recognition": True}
    
    response = client.post("/ocr", files=files, data=data)
    
    # 注意：这个测试可能会失败，因为需要实际的MinerU模型
    # 在CI/CD环境中可能需要mock
    if response.status_code == 200:
        data = response.json()
        assert "question_text" in data
        assert "question_images" in data
    else:
        # 如果模型未加载，应该返回500错误
        assert response.status_code == 500


def test_ocr_endpoint_with_invalid_file(client):
    """测试OCR接口（无效文件）"""
    files = {"image": ("test.txt", b"not an image", "text/plain")}
    data = {"enable_formula_recognition": True}
    
    response = client.post("/ocr", files=files, data=data)
    assert response.status_code == 400
    
    data = response.json()
    assert "文件类型无效" in data["detail"]





def test_analyze_text_endpoint_with_valid_image(client, sample_image):
    """测试文本分析接口（有效图像）"""
    files = {"image": ("test.png", sample_image, "image/png")}

    response = client.post("/analyze-text", files=files)

    # 注意：这个测试可能会失败，因为需要实际的MinerU模型
    # 在CI/CD环境中可能需要mock
    if response.status_code == 200:
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "question_text" in data["data"]
        assert "question_images" in data["data"]

        # 验证question_text是字符串
        assert isinstance(data["data"]["question_text"], str)

        # 验证question_images是列表
        assert isinstance(data["data"]["question_images"], list)

    else:
        # 如果模型未加载，应该返回500错误
        assert response.status_code == 500


def test_analyze_text_endpoint_with_invalid_file(client):
    """测试文本分析接口（无效文件）"""
    files = {"image": ("test.txt", b"not an image", "text/plain")}

    response = client.post("/analyze-text", files=files)
    assert response.status_code == 400

    data = response.json()
    assert "文件类型无效" in data["detail"]


def test_api_endpoints_comparison(client, sample_image):
    """对比测试不同API端点的响应格式"""
    files = {"image": ("test.png", sample_image, "image/png")}

    # 测试OCR端点
    ocr_response = client.post("/ocr", files=files, data={"enable_formula_recognition": True})

    # 测试图像提取端点
    extract_response = client.post("/extract-image", files=files)

    # 测试文本分析端点
    analyze_response = client.post("/analyze-text", files=files)

    # 如果所有端点都成功，验证响应格式的一致性
    if all(r.status_code == 200 for r in [ocr_response, extract_response, analyze_response]):
        ocr_data = ocr_response.json()
        extract_data = extract_response.json()
        analyze_data = analyze_response.json()

        # 验证所有响应都有success字段
        assert all("success" in data for data in [ocr_data, extract_data, analyze_data])

        # 验证OCR和文本分析端点返回相同的数据结构
        assert "question_text" in ocr_data
        assert "question_text" in analyze_data["data"]
        assert "question_images" in ocr_data
        assert "question_images" in analyze_data["data"]

        # 验证图像提取端点返回不同的数据结构
        assert "processed_image" in extract_data["data"]
        assert "figures" in extract_data["data"]
        assert "figure_count" in extract_data["data"]



