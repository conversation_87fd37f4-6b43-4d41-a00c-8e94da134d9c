"""
分析器模块的单元测试
"""

import pytest
from PIL import Image
import io
import base64
from unittest.mock import Mock, patch
from m_service.core.analyzer import MineruQuestionAnalyzer

from m_service.infrastructure.logging import DLog


@pytest.fixture
def mock_analyzer():
    """创建模拟的分析器实例"""
    with patch('m_service.core.analyzer.YOLOLayoutDetector'):
        analyzer = MineruQuestionAnalyzer()
        # 模拟预加载模型
        analyzer._preloaded_model = Mock()
        return analyzer


@pytest.fixture
def sample_image_bytes():
    """创建示例图像字节数据"""
    image = Image.new('RGB', (200, 200), color='white')
    img_buffer = io.BytesIO()
    image.save(img_buffer, format='PNG')
    return img_buffer.getvalue()


def test_generate_figure_paths(mock_analyzer):
    """测试图片路径生成"""
    figure_dir = "/test/dir"
    image_id = "test_image"
    figure_count = 3

    paths = mock_analyzer._generate_figure_paths(figure_dir, image_id, figure_count)

    assert len(paths) == 3
    # 使用os.path.join来处理跨平台路径
    import os
    expected_path_0 = os.path.join(figure_dir, f"{image_id}_0.jpg")
    expected_path_1 = os.path.join(figure_dir, f"{image_id}_1.jpg")
    expected_path_2 = os.path.join(figure_dir, f"{image_id}_2.jpg")

    assert paths[0] == expected_path_0
    assert paths[1] == expected_path_1
    assert paths[2] == expected_path_2


def test_load_figures_as_schema_file_not_found(mock_analyzer):
    """测试加载不存在的图片文件"""
    figure_paths = ["/nonexistent/path1.jpg", "/nonexistent/path2.jpg"]
    
    figures_schema = mock_analyzer._load_figures_as_schema(figure_paths)
    
    assert len(figures_schema) == 2
    # 当文件不存在时，应该使用文件路径作为备选
    assert figures_schema[0].name == "path1.jpg"
    assert figures_schema[0].value == "/nonexistent/path1.jpg"
    assert figures_schema[0].type == "jpg"


@patch('m_service.core.analyzer.os.path.exists')
@patch('m_service.core.analyzer.os.makedirs')
def test_extract_image_and_figures(mock_makedirs, mock_exists, mock_analyzer, sample_image_bytes):
    """测试图像和图片提取"""
    mock_exists.return_value = True
    
    # 模拟布局检测器的返回值
    mock_analyzer.layout_detector.do_detection.return_value = (
        Mock(),  # result
        2,       # figure_count
        sample_image_bytes  # masked_image
    )
    
    masked_image, figure_paths, figure_count = mock_analyzer.extract_image_and_figures(
        image_bytes=sample_image_bytes,
        image_id="test_id",
        figure_dir="/test/figures"
    )
    
    assert figure_count == 2
    assert len(figure_paths) == 2
    assert figure_paths[0] == "/test/figures/test_id_0.jpg"
    assert figure_paths[1] == "/test/figures/test_id_1.jpg"
    
    # 验证布局检测器被正确调用
    mock_analyzer.layout_detector.do_detection.assert_called_once()


def test_restore_figure_references(mock_analyzer):
    """测试图片引用恢复"""
    from m_service.models.schema import Image as SchemaImage
    
    text = "这是一个测试文本 [?? 0] 和另一个图片 [?? 1]"
    question_images = [
        SchemaImage(name="image1.jpg", value="base64data1", type="jpg"),
        SchemaImage(name="image2.jpg", value="base64data2", type="jpg")
    ]
    
    result = mock_analyzer.restore_figure_references(text, question_images)
    
    expected = "这是一个测试文本 \n\n![](image1.jpg) 和另一个图片 \n\n![](image2.jpg)"
    assert result == expected


def test_restore_figure_references_out_of_range(mock_analyzer):
    """测试图片引用超出范围的情况"""
    from m_service.models.schema import Image as SchemaImage
    
    text = "这是一个测试文本 [?? 0] 和超出范围的图片 [?? 5]"
    question_images = [
        SchemaImage(name="image1.jpg", value="base64data1", type="jpg")
    ]
    
    result = mock_analyzer.restore_figure_references(text, question_images)
    
    # 超出范围的引用应该保持原样
    expected = "这是一个测试文本 \n\n![](image1.jpg) 和超出范围的图片 [?? 5]"
    assert result == expected


def test_restore_figure_references_latex_conversion(mock_analyzer):
    """测试LaTeX公式分隔符转换"""
    from m_service.models.schema import Image as SchemaImage

    text = "公式 \\(x^2\\) 和 \\[y^2\\] 应该转换"
    question_images = []

    result = mock_analyzer.restore_figure_references(text, question_images)

    expected = "公式 $x^2$ 和 $y^2$ 应该转换"
    assert result == expected


@patch('m_service.core.analyzer.os.path.exists')
def test_process_text_with_figure_references(mock_exists, mock_analyzer):
    """测试文本和图片引用处理"""
    mock_exists.return_value = False  # 模拟文件不存在

    text = "这是测试文本 [?? 0] 和 [?? 1]"
    figure_paths = ["/test/figure_0.jpg", "/test/figure_1.jpg"]

    result = mock_analyzer.process_text_with_figure_references(text, figure_paths)

    # 验证返回的是QuestionAnalysisResult对象
    from m_service.models.schema import QuestionAnalysisResult
    assert isinstance(result, QuestionAnalysisResult)

    # 验证文本被正确处理
    assert "![](figure_0.jpg)" in result.question_text
    assert "![](figure_1.jpg)" in result.question_text

    # 验证图片列表
    assert len(result.question_images) == 2
    assert result.question_images[0].name == "figure_0.jpg"
    assert result.question_images[1].name == "figure_1.jpg"


def test_process_text_with_figure_references_empty_figures(mock_analyzer):
    """测试没有图片的文本处理"""
    text = "这是没有图片的文本"
    figure_paths = []

    result = mock_analyzer.process_text_with_figure_references(text, figure_paths)

    from m_service.models.schema import QuestionAnalysisResult
    assert isinstance(result, QuestionAnalysisResult)
    assert result.question_text == text
    assert len(result.question_images) == 0


def test_process_text_with_figures_schema(mock_analyzer):
    """测试直接使用figures_schema的文本处理"""
    from m_service.models.schema import Image as SchemaImage

    text = "这是测试文本 [?? 0] 和 [?? 1]"
    figures_schema = [
        SchemaImage(name="image1.jpg", value="base64data1", type="jpg"),
        SchemaImage(name="image2.png", value="base64data2", type="png")
    ]

    result = mock_analyzer.process_text_with_figures_schema(text, figures_schema)

    from m_service.models.schema import QuestionAnalysisResult
    assert isinstance(result, QuestionAnalysisResult)

    # 验证文本被正确处理
    assert "![](image1.jpg)" in result.question_text
    assert "![](image2.png)" in result.question_text

    # 验证图片列表
    assert len(result.question_images) == 2
    assert result.question_images[0].name == "image1.jpg"
    assert result.question_images[1].name == "image2.png"


def test_process_text_with_figures_schema_empty(mock_analyzer):
    """测试没有图片的figures_schema处理"""
    from m_service.models.schema import QuestionAnalysisResult

    text = "这是没有图片的文本"
    figures_schema = []

    result = mock_analyzer.process_text_with_figures_schema(text, figures_schema)

    assert isinstance(result, QuestionAnalysisResult)
    assert result.question_text == text
    assert len(result.question_images) == 0
