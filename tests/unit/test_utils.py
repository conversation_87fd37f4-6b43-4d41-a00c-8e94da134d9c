"""
工具函数的单元测试
"""

import pytest
from PIL import Image
import io
from m_service.utils.file_utils import calculate_bytes_hash
from m_service.utils.image_utils import (
    convert_image_to_bytes,
    convert_image_to_image,
    validate_image
)


def test_calculate_bytes_hash():
    """测试字节哈希计算"""
    test_data = b"test data"
    
    # 测试MD5
    md5_hash = calculate_bytes_hash(test_data, "md5")
    assert len(md5_hash) == 32
    assert md5_hash.isupper()
    
    # 测试SHA256
    sha256_hash = calculate_bytes_hash(test_data, "sha256")
    assert len(sha256_hash) == 64
    assert sha256_hash.islower()


def test_image_conversion():
    """测试图像转换功能"""
    # 创建测试图像
    test_image = Image.new('RGB', (100, 100), color='red')
    
    # 转换为字节
    image_bytes = convert_image_to_bytes(test_image)
    assert isinstance(image_bytes, bytes)
    assert len(image_bytes) > 0
    
    # 从字节转换回图像
    converted_image = convert_image_to_image(image_bytes)
    assert isinstance(converted_image, Image.Image)
    assert converted_image.size == (100, 100)


def test_validate_image():
    """测试图像验证"""
    # 创建有效图像
    test_image = Image.new('RGB', (50, 50), color='blue')
    image_bytes = convert_image_to_bytes(test_image)
    
    assert validate_image(image_bytes) is True
    
    # 测试无效数据
    invalid_data = b"not an image"
    assert validate_image(invalid_data) is False
