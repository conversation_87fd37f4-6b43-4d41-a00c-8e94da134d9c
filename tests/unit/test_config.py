"""
配置模块的单元测试
"""

import pytest
from m_service.core.config import Settings


def test_settings_default_values():
    """测试配置的默认值"""
    settings = Settings()
    
    assert settings.APP_NAME == "MinerU Service"
    assert settings.APP_VERSION == "1.0.0"
    assert settings.DEBUG is False
    assert settings.SERVER_HOST == "0.0.0.0"
    assert settings.SERVER_PORT == 8000
    assert settings.SERVER_WORKERS == 1


def test_settings_mineru_config():
    """测试MinerU相关配置"""
    settings = Settings()
    
    assert settings.MINERU_DEVICE == "cuda"
    assert settings.MINERU_VRAM == 8
    assert settings.MINERU_LANG == "ch"
    assert settings.MINERU_USE_LOCAL_MODEL is False


def test_settings_consul_config():
    """测试Consul相关配置"""
    settings = Settings()
    
    assert settings.CONSUL_ENABLED is False
    assert settings.LOG_LEVEL == "INFO"
