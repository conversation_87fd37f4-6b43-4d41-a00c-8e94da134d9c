"""
基础测试 - 验证包导入和基本功能
"""

import pytest


def test_package_import():
    """测试包导入"""
    try:
        import m_service
        assert hasattr(m_service, '__version__')
        assert hasattr(m_service, 'create_app')
    except ImportError as e:
        pytest.fail(f"Failed to import m_service: {e}")


def test_config_import():
    """测试配置导入"""
    try:
        from m_service.core.config import settings
        assert settings.APP_NAME == "MinerU Service"
    except ImportError as e:
        pytest.fail(f"Failed to import config: {e}")


def test_logging_import():
    """测试日志导入"""
    try:
        from m_service.infrastructure.logging import DLog
        log = DLog()
        assert log is not None
    except ImportError as e:
        pytest.fail(f"Failed to import logging: {e}")


def test_models_import():
    """测试模型导入"""
    try:
        from m_service.models.schema import HealthResponse, ErrorResponse
        from m_service.models.ocr_result import OcrResult, TextBlock
        
        # 测试基本模型创建
        health = HealthResponse(timestamp="2023-01-01T00:00:00")
        assert health.status == "healthy"
        
        error = ErrorResponse(message="test error")
        assert error.success is False
        
    except ImportError as e:
        pytest.fail(f"Failed to import models: {e}")


def test_utils_import():
    """测试工具函数导入"""
    try:
        from m_service.utils.file_utils import calculate_bytes_hash
        from m_service.utils.image_utils import validate_image
        
        # 测试基本功能
        test_data = b"test"
        hash_result = calculate_bytes_hash(test_data)
        assert len(hash_result) == 32  # MD5 hash length
        
    except ImportError as e:
        pytest.fail(f"Failed to import utils: {e}")
