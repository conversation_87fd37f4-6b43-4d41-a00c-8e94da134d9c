"""
pytest 配置文件

定义测试的全局配置和 fixtures。
"""

import pytest
from fastapi.testclient import TestClient


@pytest.fixture
def client():
    """创建测试客户端"""
    from m_service.main import create_app
    app = create_app()
    return TestClient(app)


@pytest.fixture
def sample_image():
    """提供测试用的示例图像"""
    from PIL import Image
    import io
    
    # 创建一个简单的测试图像
    image = Image.new('RGB', (100, 100), color='white')
    img_buffer = io.BytesIO()
    image.save(img_buffer, format='PNG')
    return img_buffer.getvalue()
