# 设置项目名称，避免重复命名
# name: mineru

services:
  mineru-service:
    build: .
    image: mineru-service:latest
    container_name: mineru-service
    ports:
      - "3090:3090"  # 直接暴露服务端口
    env_file:
      - .env
    volumes:
      # 模型缓存目录
      - ./models:/app/models
      - ./cache:/app/cache
      # 日志目录
      - ./logs:/app/logs
      # 开发模式：挂载代码目录（实时更新代码）
      - ./m_service:/app/m_service
      # 本地模型目录（MinerU 模型）
      - /data/models/models/OpenDataLab/PDF-Extract-Kit-1.0:/data/models/models/OpenDataLab/PDF-Extract-Kit-1.0:ro
    restart: unless-stopped
    # GPU 配置 - 使用 deploy 方式（Docker Compose v2.3+）
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    environment:
      - NVIDIA_VISIBLE_DEVICES=0  # 使用第一块 GPU
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3090/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s  # 增加启动等待时间，支持多进程模式
    networks:
      - mineru-network



# 定义网络
networks:
  mineru-network:
    driver: bridge


