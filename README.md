# MinerU Service

基于 MinerU 2.0 的专用文档分析和 OCR 服务

## 功能特性

- 🔍 **高质量 OCR**: 基于 PPOCRv5 的文本识别
- 🧮 **公式识别**: 支持数学公式检测和识别 (MFD + MFR)
- 📊 **表格识别**: 智能表格结构解析
- 📄 **布局分析**: 基于 YOLO 的文档版面分析和结构化提取
- 🎯 **题目分析**: 专门针对教育场景的题目图像分析
- ⚙️ **灵活配置**: 支持质量、性能、布局检测等多维度参数调优

## 支持的学科

- 数学 (Math)
- 物理 (Physics) 
- 化学 (Chemistry)
- 语文 (Chinese)
- 英语 (English)
- 生物 (Biology)
- 政治 (Politics)
- 历史 (History)
- 地理 (Geography)

## API 接口

### POST /ocr

对图像执行OCR，提取文本内容

**参数:**
- `image`: 图像文件
- `enable_formula_recognition`: 是否启用公式识别 (默认: true)
- `table_enable`: 是否启用表格识别 (默认: true)

**响应:**
```json
{
  "text": "识别出的文本内容"
}
```



### GET /health

健康检查接口

**响应:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "mineru_version": "2.0.6",
  "timestamp": "2024-01-01T00:00:00"
}
```

## 环境配置

### 配置说明

项目支持通过 `.env` 文件进行配置，主要配置项包括：

```bash
# 基础配置
SERVER_HOST=0.0.0.0
SERVER_PORT=3090
SERVER_WORKERS=4
DEBUG=false

# MinerU 设备配置
MINERU_DEVICE=cuda  # 或 cpu
MINERU_VRAM=8       # GPU显存大小(GB)
MINERU_LANG=zh      # 语言设置

# 本地模型配置 (可选)
MINERU_USE_LOCAL_MODEL=false
MINERU_LOCAL_MODEL_PATH=D:\py\models\mineru2.0
```

> **注意**:
> - MinerU 2.0 具有自动模型下载功能，如果不指定本地模型路径，会自动下载所需模型
> - 默认配置使用 GPU 加速 (`MINERU_DEVICE=cuda`)，如果没有 GPU 请改为 `MINERU_DEVICE=cpu`
> - 服务默认运行在端口 3090，支持 4 个工作进程

## 安装和运行

```bash
# 安装依赖
pip install -r requirements.txt

# 下载 MinerU 模型
mineru-models-download -s huggingface -m all

# 启动服务
python -m service.main

# 服务将在 http://localhost:3090 启动
# API 文档: http://localhost:3090/docs
```

## 技术栈

- **FastAPI**: Web 框架
- **MinerU 2.0**: 文档分析引擎
- **PPOCRv5**: OCR 识别引擎
- **Pydantic**: 数据验证
- **Loguru**: 日志管理
