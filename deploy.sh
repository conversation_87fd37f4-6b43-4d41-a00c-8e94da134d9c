#!/bin/bash

# MinerU Service 统一部署脚本
# 支持构建、启动、停止、重启等操作

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SERVICE_NAME="mineru-service"
BASE_IMAGE="mineru-base"

echo -e "${BLUE}🚀 MinerU Service 部署管理工具${NC}"
echo "================================"

# 显示帮助信息
show_help() {
    echo "使用方法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  build [type]    - 构建 Docker 镜像"
    echo "    type: base, fast, minimal, fixed (默认: fast)"
    echo "  start [mode]    - 启动服务"
    echo "    mode: cpu, gpu (默认: gpu)"
    echo "  stop            - 停止服务"
    echo "  restart [mode]  - 重启服务"
    echo "  status          - 查看服务状态"
    echo "  logs            - 查看服务日志"
    echo "  clean           - 清理 Docker 资源"
    echo "  test            - 测试模块导入"
    echo "  help            - 显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build base   # 构建基础镜像"
    echo "  $0 start gpu    # 启动 GPU 模式"
    echo "  $0 logs         # 查看日志"
}

# 检查环境
check_environment() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo -e "${RED}❌ Docker Compose 未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker 环境检查通过${NC}"
}

# 创建必要目录
setup_directories() {
    echo -e "${BLUE}📁 创建必要目录...${NC}"
    mkdir -p models cache logs
    chmod 755 models cache logs
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 构建镜像
build_image() {
    local build_type=${1:-fast}
    
    check_environment
    setup_directories
    
    echo -e "${BLUE}🔨 构建 Docker 镜像 (${build_type})...${NC}"
    
    if [ -f "./docker-build-fast.sh" ]; then
        chmod +x ./docker-build-fast.sh
        ./docker-build-fast.sh $build_type
    else
        echo -e "${RED}❌ 找不到构建脚本${NC}"
        exit 1
    fi
}

# 启动服务
start_service() {
    local mode=${1:-gpu}
    
    check_environment
    setup_directories
    
    echo -e "${BLUE}🚀 启动服务 (${mode} 模式)...${NC}"
    
    if [ "$mode" = "gpu" ]; then
        docker-compose --profile gpu up -d mineru-service-gpu
    else
        docker-compose up -d mineru-service
    fi
    
    echo -e "${GREEN}✅ 服务启动完成${NC}"
    echo -e "${BLUE}📍 服务地址: http://localhost:3090${NC}"
    echo -e "${BLUE}📚 API 文档: http://localhost:3090/docs${NC}"
}

# 停止服务
stop_service() {
    echo -e "${BLUE}🛑 停止服务...${NC}"
    docker-compose down
    echo -e "${GREEN}✅ 服务已停止${NC}"
}

# 重启服务
restart_service() {
    local mode=${1:-gpu}
    echo -e "${BLUE}🔄 重启服务...${NC}"
    stop_service
    sleep 2
    start_service $mode
}

# 查看状态
show_status() {
    echo -e "${BLUE}📊 服务状态:${NC}"
    docker-compose ps
    echo ""
    echo -e "${BLUE}💾 镜像信息:${NC}"
    docker images | grep -E "(mineru|REPOSITORY)" || echo "未找到相关镜像"
}

# 查看日志
show_logs() {
    echo -e "${BLUE}📋 服务日志:${NC}"
    docker-compose logs -f --tail=100
}

# 清理资源
clean_resources() {
    echo -e "${YELLOW}⚠️  这将删除所有 MinerU 相关的 Docker 资源${NC}"
    read -p "确认继续？(y/N): " -n 1 -r
    echo

    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}🧹 清理 Docker 资源...${NC}"

        # 停止服务
        docker-compose down -v 2>/dev/null || true

        # 删除镜像
        docker rmi ${SERVICE_NAME} ${BASE_IMAGE} 2>/dev/null || true

        # 清理未使用资源
        docker system prune -f

        echo -e "${GREEN}✅ 清理完成${NC}"
    else
        echo -e "${BLUE}💾 取消清理${NC}"
    fi
}

# 测试模块导入
test_modules() {
    echo -e "${BLUE}🧪 测试模块导入...${NC}"

    if [ -f "test-consul.py" ]; then
        python test-consul.py
    else
        echo -e "${RED}❌ 找不到测试脚本${NC}"
    fi
}

# 主逻辑
case "${1:-help}" in
    "build")
        build_image $2
        ;;
    "start")
        start_service $2
        ;;
    "stop")
        stop_service
        ;;
    "restart")
        restart_service $2
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "clean")
        clean_resources
        ;;
    "test")
        test_modules
        ;;
    "help"|*)
        show_help
        ;;
esac
