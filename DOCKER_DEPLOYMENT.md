# MinerU Service Docker 部署指南

## 🐳 Docker 容器化部署

### 快速启动

```bash
# 一键启动（推荐）
./docker-start.sh

# 或手动启动
docker-compose up -d
```

### 停止服务

```bash
# 一键停止
./docker-stop.sh

# 或手动停止
docker-compose down
```

## 📋 部署要求

### 系统要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 内存
- 至少 10GB 磁盘空间（用于模型存储）

### GPU 支持（可选）
如需 GPU 加速，请确保：
- 安装 NVIDIA Docker Runtime
- 配置 GPU 驱动

```bash
# 安装 NVIDIA Docker Runtime
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

## ⚙️ 配置说明

### 环境变量配置

创建 `.env` 文件进行配置：

```bash
# 应用配置
DEBUG=false
LOG_LEVEL=INFO
SERVER_HOST=0.0.0.0
SERVER_PORT=3090
SERVER_WORKERS=1

# MinerU 设备配置
MINERU_DEVICE=cuda          # cpu/cuda
MINERU_VRAM=8              # GPU 显存大小(GB)
MINERU_LANG=ch             # 语言设置
MINERU_MODEL_SOURCE=huggingface  # 模型源

# 质量优化参数
MINERU_OCR_CONFIDENCE_THRESHOLD=0.7
MINERU_MFD_CONFIDENCE_THRESHOLD=0.4
MINERU_MFR_CONFIDENCE_THRESHOLD=0.7
MINERU_LAYOUT_CONFIDENCE_THRESHOLD=0.6

# 性能优化参数
MINERU_MIN_BATCH_INFERENCE_SIZE=150
MINERU_OCR_BATCH_SIZE=10
MINERU_MFR_BATCH_SIZE=6
MINERU_LAYOUT_BATCH_SIZE=8
MINERU_MFD_BATCH_SIZE=8
MINERU_TABLE_BATCH_SIZE=4
MINERU_MAX_BATCH_SIZE=32
```

### 目录挂载

Docker Compose 会自动创建以下目录：

```
./models/   # 模型文件存储
./cache/    # 缓存文件存储  
./logs/     # 日志文件存储
```

## 🌐 服务访问

启动成功后，可通过以下地址访问：

- **主服务**: http://localhost:3090
- **API 文档**: http://localhost:3090/docs

## 🔧 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f mineru-service

# 重启服务
docker-compose restart

# 进入容器
docker-compose exec mineru-service bash

# 更新镜像
docker-compose pull
docker-compose up -d

# 清理资源
docker-compose down --volumes --rmi all
```

## 🐛 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs mineru-service
   
   # 检查配置
   docker-compose config
   ```

2. **GPU 不可用**
   ```bash
   # 检查 GPU 支持
   docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi
   ```

3. **模型下载失败**
   ```bash
   # 手动下载模型
   docker-compose exec mineru-service bash
   mineru-models-download -s huggingface -m all
   ```

4. **内存不足**
   - 减少批处理大小
   - 降低 VRAM 设置
   - 使用 CPU 模式

### 性能优化

1. **GPU 优化**
   ```bash
   # 设置合适的 VRAM
   MINERU_VRAM=8
   
   # 调整批处理大小
   MINERU_OCR_BATCH_SIZE=10
   MINERU_MFR_BATCH_SIZE=6
   ```

2. **CPU 优化**
   ```bash
   # 使用 CPU 模式
   MINERU_DEVICE=cpu
   
   # 减少批处理大小
   MINERU_OCR_BATCH_SIZE=4
   MINERU_MFR_BATCH_SIZE=2
   ```

## 📊 监控和日志

### 健康检查

服务包含内置健康检查：
- 检查间隔：30秒
- 超时时间：10秒
- 重试次数：3次

### 日志管理

日志文件位置：
- 应用日志：`./logs/`

```bash
# 查看应用日志
tail -f logs/mineru-service.log
```

## 🔄 更新部署

```bash
# 停止服务
docker-compose down

# 拉取最新代码
git pull

# 重新构建镜像
docker-compose build --no-cache

# 启动服务
docker-compose up -d
```

## 🔒 安全建议

1. **网络安全**
   - 使用防火墙限制访问
   - 配置 HTTPS（生产环境）
   - 定期更新镜像

2. **数据安全**
   - 定期备份模型和配置
   - 限制文件上传大小
   - 监控磁盘使用情况

3. **访问控制**
   - 配置认证机制（如需要）
   - 限制 API 访问频率
   - 记录访问日志
