from typing import Optional
import random

import consul as python_consul  # 确保使用别名

class ConsulManager:
    @staticmethod
    async def register_service(
        service_name: str,
        service_id_name: str,
        service_host: str,
        service_port: int,
        host: str,
        port: int,
        health_check_url: str = "/health",
        interval: str = "5s"
    ) -> str:
        client = python_consul.Consul(host=host, port=port)
        service_id = f"{service_id_name}"
        
        client.agent.service.register(
            name=service_name,
            service_id=service_id,
            address=service_host,
            port=service_port,
            check=python_consul.Check.http(
                url=f"http://{service_host}:{service_port}{health_check_url}",
                interval=interval
            )
        )
        return service_id
        
    @staticmethod
    async def deregister_service(
        service_id: str,
        host: str ,
        port: int
    ) -> None:
        client = python_consul.Consul(host=host, port=port)
        client.agent.service.deregister(service_id)

    @staticmethod
    def get_service_url_by_id(service_id_name: str, host: str,port: int) -> Optional[str]:
        """根据服务ID获取服务地址"""
        try:
            client = python_consul.Consul(host=host, port=port)
            services = client.agent.services()
            if service_id_name not in services:
                return None
            service = services[service_id_name]
            return f"http://{service['Address']}:{service['Port']}"
        except Exception as e: 
            print(f"Consul连接异常: {e}")
            return None

    @staticmethod
    def get_healthy_service(service_name: str,host: str,port: int,strategy: str = "random") -> Optional[str]:
        """获取健康服务实例，支持多种负载均衡策略"""
        try:
            client = python_consul.Consul(host=host, port=port)
            _, instances = client.health.service(service_name, passing=True)
            
            if not instances:
                raise Exception(f"No healthy instances available for {service_name}")

            # 负载均衡策略选择
            if strategy == "random":
                instance = random.choice(instances)
            elif strategy == "round_robin":
                index = 0  # 需要持久化状态
                instance = instances[index % len(instances)]
            else:
                instance = instances[0]

            service = instance['Service']
            return f"http://{service['Address']}:{service['Port']}"

        except Exception as e:
            print(f"Consul连接异常: {e}")
            return None
