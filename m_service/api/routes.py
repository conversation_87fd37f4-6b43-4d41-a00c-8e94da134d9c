"""
API 路由
"""

from datetime import datetime
from typing import Optional
from fastapi import APIRouter, File, Form, UploadFile, HTTPException, Depends
import threading
from concurrent.futures import ThreadPoolExecutor
import traceback

from m_service.models.schema import (
    HealthResponse,
    OcrTextResponse,
)
from m_service.core.analyzer import MineruQuestionAnalyzer
from m_service.core.config import settings
from m_service.infrastructure.logging import DLog

router = APIRouter()

# 并发分析器管理
_analyzer_instance = None
_analyzer_lock = threading.Lock()
_executor = None


def get_logger() -> DLog:
    """获取日志实例（依赖注入）"""
    return DLog()


def get_analyzer(log: DLog = Depends(get_logger)) -> MineruQuestionAnalyzer:
    """获取分析器实例（线程安全的单例模式）"""
    global _analyzer_instance, _executor
    if _analyzer_instance is None:
        with _analyzer_lock:
            # 双重检查锁定模式
            if _analyzer_instance is None:
                log.infoln("初始化 MinerU 分析器...")
                _analyzer_instance = MineruQuestionAnalyzer(log=log)
                log.infoln("MinerU 分析器初始化完成")

                # 初始化线程池
                if _executor is None:
                    max_workers = settings.MAX_CONCURRENT_REQUESTS
                    _executor = ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="mineru-worker")
                    log.infoln(f"初始化线程池，最大并发数: {max_workers}")
    return _analyzer_instance


def create_analyzer_instance(log: Optional[DLog] = None) -> MineruQuestionAnalyzer:
    """创建分析器实例（用于非依赖注入场景）"""
    global _analyzer_instance
    if _analyzer_instance is None:
        if log is None:
            log = DLog()
        log.infoln("初始化 MinerU 分析器...")
        _analyzer_instance = MineruQuestionAnalyzer(log=log)
        log.infoln("MinerU 分析器初始化完成")
    return _analyzer_instance


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    try:
        # 尝试导入 MinerU 检查是否可用
        import mineru
        try:
            from mineru.version import __version__ as mineru_version
        except ImportError:
            mineru_version = getattr(mineru, '__version__', 'unknown')
    except ImportError:
        mineru_version = None
    
    return HealthResponse(
        status="healthy",
        version=settings.APP_VERSION,
        mineru_version=mineru_version,
        timestamp=datetime.now().isoformat()
    )


@router.post(
    "/ocr",
    response_model=OcrTextResponse,
    summary="对图像执行OCR",
    description="上传图像文件以提取文本信息。"
)
async def ocr_from_image(
    image: UploadFile = File(..., description="需要处理的图像文件。"),
    enable_formula_recognition: bool = Form(True, description="为科学内容启用公式识别。"),
    table_enable: bool = Form(True, description="是否启动表格识别"),
    analyzer: MineruQuestionAnalyzer = Depends(get_analyzer),
    log: DLog = Depends(get_logger)
):
    """
    此端点为图像提供OCR解决方案，并返回提取的文本。
    """
    if not image.content_type or not image.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="文件类型无效，请上传图片。")

    try:
        image_bytes = await image.read()
        
        text_result = analyzer.analyze_question_content_ocr(
            image_bytes=image_bytes,
            enable_formula_recognition=enable_formula_recognition,
            table_enable=table_enable
        )

        if text_result is not None:
            return OcrTextResponse(text=text_result)
        else:
            raise HTTPException(
                status_code=500,
                detail="OCR处理失败或未返回结果。",
            )

    except Exception as e:
        log.errorln(f"处理OCR请求时出错: {e}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"发生内部服务器错误: {e}")



