"""
MinerU Service 主应用
"""

import sys
import os
import re

# 确保项目根目录在 Python 路径中，以便进行绝对导入
# 这必须在任何 "from m_service..." 导入之前完成
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 使用绝对导入 - 现代 Python 项目的最佳实践
from m_service.core.config import settings
from m_service.api.routes import router
from m_service.models.schema import ErrorResponse
from m_service.infrastructure.consul import ConsulManager
from m_service.infrastructure.logging import DLog


def setup_logging():
    """设置日志"""
    # 使用 DLog 设置日志级别
    DLog.set_log(DLog.get_level(settings.LOG_LEVEL))


def get_logger():
    """获取日志实例（依赖注入）"""
    return DLog()


def _warmup_mineru_models():
    """预热 MinerU 模型，避免并发时重复初始化"""
    try:
        log = DLog()
        log.infoln("🔥 开始预热 MinerU 模型...")

        # 创建一个临时的分析器实例来触发模型加载
        from m_service.core.analyzer import MineruQuestionAnalyzer
        analyzer = MineruQuestionAnalyzer(log=log)

        # 创建一个小的测试图像来触发模型初始化
        from PIL import Image
        import io

        # 创建一个简单的测试图像
        test_image = Image.new('RGB', (100, 100), color='white')
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='PNG')
        test_bytes = img_buffer.getvalue()

        # 执行一次分析来预热模型
        log.infoln("执行测试分析以预热模型...")
        analyzer.analyze_question_content_ocr(
            image_bytes=test_bytes,
            enable_formula_recognition=False, # 不需要公式识别，只需要触发模型加载
            table_enable=False  # 不需要表格识别
        )

        log.infoln("✅ MinerU 模型预热完成")

    except Exception as e:
        log = DLog()
        log.warningln(f"MinerU 模型预热失败，将在首次使用时初始化: {e}")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 在此生命周期事件中配置日志，以确保它在Uvicorn默认配置之后执行
    setup_uvicorn_dlog()
    
    log = DLog()
    # 启动时执行
    log.infoln("🚀 MinerU Service 正在启动...")

    try:
        # 验证 MinerU 是否可用
        import mineru
        try:
            from mineru.version import __version__ as mineru_version
        except ImportError:
            mineru_version = getattr(mineru, '__version__', 'unknown')
        log.infoln(f"✅ MinerU 版本: {mineru_version}")

        # 预热模型（多进程模式下需要特殊处理）
        should_warmup = not settings.DEBUG or settings.SERVER_WORKERS > 1
        if should_warmup:
            # CPU 模式下的多进程特殊处理
            if settings.SERVER_WORKERS > 1 and settings.MINERU_DEVICE == "cpu":
                import random
                import time
                # CPU 模式下增加更长的延迟，避免资源竞争
                worker_delay = random.uniform(2.0, 8.0)  # 2-8秒随机延迟
                log.infoln(f"🔄 CPU多进程模式：工作进程 {os.getpid()} 延迟 {worker_delay:.1f}s 后开始预热")
                time.sleep(worker_delay)
            elif settings.SERVER_WORKERS > 1:
                import random
                import time
                worker_delay = random.uniform(0.5, 3.0)  # GPU模式较短延迟
                log.infoln(f"🔄 多进程模式：工作进程 {os.getpid()} 延迟 {worker_delay:.1f}s 后开始预热")
                time.sleep(worker_delay)

            log.infoln("🔥 预热 MinerU 模型...")
            try:
                # 在多进程模式下，使用更保守的预热策略
                if settings.SERVER_WORKERS > 1:
                    log.infoln("🔄 多进程模式：使用延迟初始化策略")
                    # 不在启动时预热，而是在首次请求时初始化
                    log.infoln("✅ 多进程模式预热策略设置完成")
                else:
                    from m_service.api.routes import create_analyzer_instance
                    create_analyzer_instance(log)  # 单进程模式正常预热
                    log.infoln("✅ MinerU 模型预热完成")

                if settings.SERVER_WORKERS > 1:
                    log.infoln(f"🔄 多进程模式：工作进程 {os.getpid()} 启动完成")
            except ImportError:
                # 如果相对导入失败，跳过预热
                log.warningln("⚠️  跳过模型预热（导入问题）")
            except Exception as e:
                log.errorln(f"❌ 模型预热失败: {e}")
                # 不要因为预热失败就退出，让服务继续启动
                log.warningln("⚠️  将在首次请求时初始化模型")
        else:
            log.infoln("🔧 DEBUG模式：跳过模型预热")
        #开启consul(可选)
        if  settings.CONSUL_ENABLED:
            # 为每个工作进程生成唯一的服务ID
            worker_pid = os.getpid()
            unique_service_id = f"{settings.SERVICE_NAME}-{worker_pid}"

            # 使用对外访问端口（Nginx 端口）进行 Consul 注册
            consul_port = settings.CONSUL_SERVICE_PORT or settings.SERVER_PORT

            await ConsulManager.register_service(
                service_name=settings.SERVICE_NAME,
                service_id_name=unique_service_id,
                service_host=settings.SERVICE_HOST,
                service_port=consul_port,
                host=settings.CONSUL_HOST,
                port=settings.CONSUL_PORT,
            )
            log.infoln(f"✅ consul开启成功 (服务ID: {unique_service_id}, 端口: {consul_port})")

            # 保存服务ID用于注销
            settings._consul_service_id = unique_service_id

        log.infoln("✅ MinerU Service 启动完成")

    except ImportError as e:
        log.errorln(f"❌ MinerU 导入失败: {e}")
        log.errorln("请确保已正确安装 MinerU 2.0")
        sys.exit(1)
    except Exception as e:
        log.errorln(f"❌ 服务启动失败: {e}")
        sys.exit(1)
    
    yield
    if settings.CONSUL_ENABLED:
        # 使用保存的唯一服务ID进行注销
        service_id = getattr(settings, '_consul_service_id', settings.SERVICE_NAME)
        await ConsulManager.deregister_service(
            service_id=service_id,
            host=settings.CONSUL_HOST,
            port=settings.CONSUL_PORT,
        )
    # 关闭时执行
    log.infoln("🛑 MinerU Service 正在关闭...")


def create_app() -> FastAPI:
    """创建 FastAPI 应用"""

    # 设置日志
    setup_logging()

    # 设置 MinerU 环境变量（在日志初始化后）
    settings.setup_mineru_env()

    # 预热 MinerU 模型（避免并发时重复初始化）
    # 注意：在多进程模式下，每个工作进程都会在启动时预热模型
    if settings.SERVER_WORKERS <= 1:
        _warmup_mineru_models()
    else:
        log = DLog()
        log.infoln("🔄 多进程模式：每个工作进程将在启动时独立预热模型")
        log.infoln(f"📊 预计启动 {settings.SERVER_WORKERS} 个工作进程")

    # 创建应用
    app = FastAPI(
        title=settings.APP_NAME,
        version=settings.APP_VERSION,
        description="基于 MinerU 2.0 的专用文档分析和 OCR 服务",
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json"
    )
    
    # 添加 CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(router, prefix="", tags=["MinerU"])
    
    # 全局异常处理器
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content=ErrorResponse(
                success=False,
                message=exc.detail,
                error_code=str(exc.status_code)
            ).model_dump()
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                success=False,
                message="服务器内部错误",
                error_code="500",
                details={"error": str(exc)} if settings.DEBUG else None
            ).model_dump()
        )
    
    return app


# 创建应用实例
app = create_app()


def setup_uvicorn_dlog():
    """配置 Uvicorn 使用 DLog"""
    import logging
    import re

    class DLogHandler(logging.Handler):
        """将标准 logging 转换为 DLog 的处理器"""

        def __init__(self):
            super().__init__()
            self.dlog = DLog()
            # 用于解析Uvicorn访问日志的正则表达式
            self.access_log_pattern = re.compile(
                r'"(?P<method>[A-Z]+) (?P<path>.*?) HTTP/\d\.\d" (?P<status_code>\d+)'
            )

        def emit(self, record):
            """处理日志记录"""
            try:
                msg = self.format(record)

                # 特别处理 uvicorn.access 日志
                if record.name == "uvicorn.access":
                    match = self.access_log_pattern.search(msg)
                    if match:
                        log_data = match.groupdict()
                        # 格式化为更具可读性的消息
                        formatted_msg = (
                            f"✅ 请求处理完成: "
                            f"{log_data['method']} {log_data['path']} "
                            f"| 状态码: {log_data['status_code']}"
                        )
                        self.dlog.infoln(formatted_msg)
                    else:
                        # 如果正则不匹配，则按原样打印
                        self.dlog.infoln(msg)
                    return

                # 根据日志级别调用相应的 DLog 方法
                if record.levelno >= logging.ERROR:
                    self.dlog.errorln(msg)
                elif record.levelno >= logging.WARNING:
                    self.dlog.warningln(msg)
                elif record.levelno >= logging.INFO:
                    self.dlog.infoln(msg)
                else:
                    self.dlog.debugln(msg)
            except Exception:
                self.handleError(record)

    class HealthCheckFilter(logging.Filter):
        """过滤掉对 /health 接口的访问日志。"""
        def filter(self, record: logging.LogRecord) -> bool:
            # record.getMessage() 会返回完整的日志字符串，如: '127.0.0.1:12345 - "GET /health HTTP/1.1" 200 OK'
            return "/health" not in record.getMessage()

    # 获取 Uvicorn 的日志器
    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_access_logger = logging.getLogger("uvicorn.access")

    # 清除现有的处理器
    uvicorn_logger.handlers.clear()
    uvicorn_access_logger.handlers.clear()

    # 添加 DLog 处理器
    dlog_handler = DLogHandler()
    dlog_handler.setFormatter(logging.Formatter("%(message)s"))

    uvicorn_logger.addHandler(dlog_handler)
    uvicorn_logger.setLevel(logging.INFO)

    # 禁用访问日志或使用 DLog 处理
    uvicorn_access_logger.addHandler(dlog_handler)
    uvicorn_access_logger.setLevel(logging.INFO)  # 恢复为INFO级别以显示访问日志
    # 添加自定义过滤器以屏蔽健康检查日志
    uvicorn_access_logger.addFilter(HealthCheckFilter())

    # 防止日志传播到根日志器
    uvicorn_logger.propagate = False
    uvicorn_access_logger.propagate = False


def main():
    """主函数"""
    import uvicorn

    log = DLog()
    log.infoln(f"🌟 启动 {settings.APP_NAME} v{settings.APP_VERSION}")
    log.infoln(f"📍 服务地址: http://{settings.SERVER_HOST}:{settings.SERVER_PORT}")
    log.infoln(f"📚 API 文档: http://{settings.SERVER_HOST}:{settings.SERVER_PORT}/docs")

    # 检查多进程配置
    if settings.SERVER_WORKERS > 1:
        log.infoln(f"🔄 检测到多进程配置 (SERVER_WORKERS={settings.SERVER_WORKERS})")
        if settings.MINERU_DEVICE == "cpu":
            log.warningln("⚠️  CPU模式下多进程可能导致资源竞争，已启用保护机制")
            log.infoln("💡 CPU多进程优化：延长启动延迟、延迟模型初始化")
        elif settings.MINERU_DEVICE == "cuda":
            log.warningln("⚠️  GPU模式下多进程可能导致显存竞争，请确保显存充足")
        log.infoln("💡 多进程模式已优化：添加启动延迟和独立服务ID避免冲突")

        # 使用应用导入字符串以支持多进程
        app_str = "m_service.main:app"
        log.infoln(f"🔄 使用多进程模式启动，工作进程数: {settings.SERVER_WORKERS}")

        uvicorn.run(
            app_str,  # 使用导入字符串而不是应用对象
            host=settings.SERVER_HOST,
            port=settings.SERVER_PORT,
            workers=settings.SERVER_WORKERS,
            reload=settings.DEBUG,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True,
            use_colors=False
        )
    else:
        # 单进程模式，直接使用应用对象
        log.infoln("🚀 使用单进程模式启动")
        uvicorn.run(
            app,
            host=settings.SERVER_HOST,
            port=settings.SERVER_PORT,
            reload=settings.DEBUG,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True,
            use_colors=False
        )


if __name__ == "__main__":
    main()
