"""
配置管理 - 参照 ai-service-center 格式
"""

import os
from decouple import config 
from typing import Optional


class Settings:
    """应用配置 - 使用 decouple 读取环境变量"""

    # 应用基础配置
    APP_NAME: str = config("APP_NAME", default="MinerU Service")
    APP_VERSION: str = config("APP_VERSION", default="1.0.0")
    DEBUG: bool = config("DEBUG", default=False, cast=bool)

    # 服务配置
    SERVER_HOST: str = config("SERVER_HOST", default="0.0.0.0")
    SERVER_PORT: int = config("SERVER_PORT", default=8000, cast=int)
    SERVER_WORKERS: int = config("SERVER_WORKERS", default=1, cast=int)

    # MinerU 配置
    MINERU_DEVICE: str = config("MINERU_DEVICE", default="cuda")
    MINERU_VRAM: int = config("MINERU_VRAM", default=8, cast=int)
    MINERU_LANG: str = config("MINERU_LANG", default="ch")
    MINERU_USE_LOCAL_MODEL: bool = config("MINERU_USE_LOCAL_MODEL", default=False, cast=bool)
    MINERU_LOCAL_MODEL_PATH: Optional[str] = config("MINERU_LOCAL_MODEL_PATH", default=None)


    # MinerU 质量优化参数
    MINERU_OCR_CONFIDENCE_THRESHOLD: float = config("MINERU_OCR_CONFIDENCE_THRESHOLD", default=0.7, cast=float)
    MINERU_MFD_CONFIDENCE_THRESHOLD: float = config("MINERU_MFD_CONFIDENCE_THRESHOLD", default=0.4, cast=float)
    MINERU_MFR_CONFIDENCE_THRESHOLD: float = config("MINERU_MFR_CONFIDENCE_THRESHOLD", default=0.7, cast=float)
    MINERU_LAYOUT_CONFIDENCE_THRESHOLD: float = config("MINERU_LAYOUT_CONFIDENCE_THRESHOLD", default=0.6, cast=float)

    # MinerU 性能优化参数
    MINERU_MIN_BATCH_INFERENCE_SIZE: int = config("MINERU_MIN_BATCH_INFERENCE_SIZE", default=150, cast=int)
    MINERU_OCR_BATCH_SIZE: int = config("MINERU_OCR_BATCH_SIZE", default=10, cast=int)
    MINERU_MFR_BATCH_SIZE: int = config("MINERU_MFR_BATCH_SIZE", default=6, cast=int)
    MINERU_LAYOUT_BATCH_SIZE: int = config("MINERU_LAYOUT_BATCH_SIZE", default=8, cast=int)
    MINERU_MFD_BATCH_SIZE: int = config("MINERU_MFD_BATCH_SIZE", default=8, cast=int)
    MINERU_TABLE_BATCH_SIZE: int = config("MINERU_TABLE_BATCH_SIZE", default=4, cast=int)
    MINERU_MAX_BATCH_SIZE: int = config("MINERU_MAX_BATCH_SIZE", default=32, cast=int)

    # MFR 性能优化参数
    MINERU_MFR_MAX_LENGTH: int = config("MINERU_MFR_MAX_LENGTH", default=512, cast=int)
    MINERU_MFR_TIMEOUT: int = config("MINERU_MFR_TIMEOUT", default=30, cast=int)

    # 并发配置
    MAX_CONCURRENT_REQUESTS: int = config("MAX_CONCURRENT_REQUESTS", default=2, cast=int)

    # Consul 配置
    CONSUL_ENABLED: bool = config("CONSUL_ENABLED", default=False, cast=bool)
    CONSUL_HOST: str = config("CONSUL_HOST", default=None)
    CONSUL_PORT: int = config("CONSUL_PORT", default=None, cast=int)
    SERVICE_NAME: str = config("SERVICE_NAME", default=None)
    SERVICE_HOST: str = config("SERVICE_HOST", default=None)
    # Consul 注册的服务端口（对外访问端口）
    CONSUL_SERVICE_PORT: int = config("CONSUL_SERVICE_PORT", default=None, cast=int)

    # 日志配置
    LOG_LEVEL: str = config("LOG_LEVEL", default="INFO")
    LOG_FILE: Optional[str] = config("LOG_FILE", default=None)

    # 临时目录配置
    TEMP_DIR: str = config("TEMP_DIR", default="./data/tmp")

    # 网络配置
    HF_ENDPOINT: Optional[str] = config("HF_ENDPOINT", default=None)
    MINERU_DOWNLOAD_RETRIES: int = config("MINERU_DOWNLOAD_RETRIES", default=3, cast=int)
    MINERU_DOWNLOAD_TIMEOUT: int = config("MINERU_DOWNLOAD_TIMEOUT", default=300, cast=int)

    # 注意：本地模型路径通过 ~/mineru.json 文件配置，不通过环境变量

    def setup_mineru_env(self):
        """设置 MinerU 环境变量 (仅设置实际使用的)"""
        from m_service.infrastructure.logging import DLog
        log = DLog()

        env_vars = {}

        # 设备配置
        env_vars["MINERU_DEVICE"] = self.MINERU_DEVICE
        env_vars["MINERU_VRAM"] = str(self.MINERU_VRAM)
        env_vars["MINERU_LANG"] = self.MINERU_LANG
        log.infoln(f"🔧 设置 MinerU 设备: {self.MINERU_DEVICE}")
        log.infoln(f"🔧 设置 MinerU 显存: {self.MINERU_VRAM}GB")
        log.infoln(f"🔧 设置 MinerU 语言: {self.MINERU_LANG}")

        # 根据 MINERU_USE_LOCAL_MODEL 决定模型源
        if self.MINERU_USE_LOCAL_MODEL:
            # 使用本地模型
            env_vars["MINERU_MODEL_SOURCE"] = "local"
            log.infoln("🔧 设置 MinerU 模型源: local (使用本地模型)")
            self._patch_mineru_config_reader()
        else:
            # 使用 mineru 内置的智能下载机制，不设置 MINERU_MODEL_SOURCE
            # 让 MinerU 2.0.6 使用其默认的自动模型管理
            log.infoln("🔧 使用 MinerU 内置智能下载机制 (自动模型管理)")

            # 设置网络相关的环境变量来改善下载稳定性
            env_vars["HF_HUB_DISABLE_PROGRESS_BARS"] = "1"  # 禁用进度条减少网络开销
            env_vars["HF_HUB_ENABLE_HF_TRANSFER"] = "1"     # 启用更快的传输

            # 设置镜像源（如果配置了）
            if self.HF_ENDPOINT:
                env_vars["HF_ENDPOINT"] = self.HF_ENDPOINT
                log.infoln(f"🔧 设置 HuggingFace 镜像源: {self.HF_ENDPOINT}")

            # 设置下载超时和重试
            env_vars["HF_HUB_DOWNLOAD_TIMEOUT"] = str(self.MINERU_DOWNLOAD_TIMEOUT)
            log.infoln(f"🔧 设置下载超时: {self.MINERU_DOWNLOAD_TIMEOUT}s")
            log.infoln(f"🔧 设置重试次数: {self.MINERU_DOWNLOAD_RETRIES}")
            log.infoln("🔧 设置网络优化参数以改善模型下载稳定性")

        # 调试模式
        if self.DEBUG:
            env_vars["MINERU_DEBUG"] = "true"
            env_vars["MINERU_LOG_LEVEL"] = "DEBUG"

        for key, value in env_vars.items():
            os.environ[key] = value

    def _patch_mineru_config_reader(self):
        """拦截 MinerU 的配置读取函数，直接从环境变量读取模型路径"""
        from m_service.infrastructure.logging import DLog
        log = DLog()

        if not self.MINERU_USE_LOCAL_MODEL:
            log.infoln("🔧 未启用本地模型，跳过配置拦截")
            return

        if not self.MINERU_LOCAL_MODEL_PATH:
            log.warningln("⚠️  启用本地模型但未设置 MINERU_LOCAL_MODEL_PATH")
            log.warningln("⚠️  将回退到 mineru 默认模型下载机制")
            return

        # 检查本地模型路径是否存在
        if not os.path.exists(self.MINERU_LOCAL_MODEL_PATH):
            log.errorln(f"❌ 本地模型路径不存在: {self.MINERU_LOCAL_MODEL_PATH}")
            log.warningln("⚠️  将回退到 mineru 默认模型下载机制")
            return

        log.infoln(f"🔧 拦截 MinerU 配置读取，使用本地模型路径: {self.MINERU_LOCAL_MODEL_PATH}")

        try:
            # 定义新的函数，直接返回环境变量中的路径
            def patched_get_local_models_dir():
                """从环境变量读取本地模型路径"""
                return {
                    'pipeline': self.MINERU_LOCAL_MODEL_PATH
                }

            # 拦截多个模块中的 get_local_models_dir 函数
            modules_to_patch = [
                'mineru.utils.config_reader',
                'mineru.utils.models_download_utils'
            ]

            patched_count = 0
            for module_name in modules_to_patch:
                try:
                    module = __import__(module_name, fromlist=['get_local_models_dir'])
                    if hasattr(module, 'get_local_models_dir'):
                        module.get_local_models_dir = patched_get_local_models_dir
                        patched_count += 1
                        log.infoln(f"✅ 成功拦截 {module_name}.get_local_models_dir")
                except ImportError as e:
                    log.warningln(f"⚠️  无法导入模块 {module_name}: {e}")
                except Exception as e:
                    log.warningln(f"⚠️  拦截模块 {module_name} 失败: {e}")

            if patched_count > 0:
                log.infoln(f"✅ 成功拦截 {patched_count} 个模块的配置读取函数")
                log.infoln(f"📁 本地模型路径: {self.MINERU_LOCAL_MODEL_PATH}")
            else:
                log.errorln("❌ 未能拦截任何模块的配置读取函数")

        except Exception as e:
            log.errorln(f"❌ 拦截 MinerU 配置读取函数失败: {e}")
            log.warningln("⚠️  将回退到原始配置方式")




# 全局配置实例
settings = Settings()

# 注意：setup_mineru_env() 将在日志系统初始化后调用
