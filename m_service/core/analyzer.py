import io
import traceback
from typing import List, Optional
from PIL import Image
import re
import numpy as np


# MinerU 2.0 API 导入
from mineru.backend.pipeline.pipeline_analyze import doc_analyze
from mineru.data.data_reader_writer import DataWriter


from m_service.models.ocr_result import OcrResult, TextBlock
from m_service.infrastructure.logging import DLog
from m_service.core.config import settings

class EmptyWriter(DataWriter):
    def write(self, path: str, data: bytes) -> None:
        pass

class MineruQuestionAnalyzer(object):
    def __init__(self, log: Optional[DLog] = None):
        self.log = log if log is not None else DLog()

        # 预加载模型
        self._preload_models()

        # 记录模型路径信息
        self._log_model_paths()


    def _preload_models(self):
        """预加载 MinerU 模型 - 简化版本"""
        try:
            self.log.infoln("🚀 开始预加载 MinerU 模型...")

            # 清理可能存在的异常状态
            import torch
            import gc

            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                self.log.infoln("已清理 GPU 缓存")

            # 强制垃圾回收
            gc.collect()

            # MinerU 2.0 使用 doc_analyze 函数时会自动初始化模型
            # 我们只需要确保环境配置正确
            self.log.infoln(f"MinerU 语言设置: {settings.MINERU_LANG}")
            self.log.infoln(f"MinerU 设备设置: {settings.MINERU_DEVICE}")

            # 标记预加载完成（实际上是配置验证完成）
            self._preloaded_model = None  # MinerU 2.0 不需要预加载模型对象
            self.log.infoln("✅ MinerU 环境配置验证完成")

        except Exception as e:
            self.log.errorln(f"MinerU 环境配置失败: {e}")
            self._preloaded_model = None


    def _check_torch_environment(self):
        """检查 PyTorch 环境"""
        try:
            import torch
            self.log.infoln(f"🔧 PyTorch 版本: {torch.__version__}")
            self.log.infoln(f"🔧 CUDA 可用: {torch.cuda.is_available()}")
            if torch.cuda.is_available():
                self.log.infoln(f"🔧 CUDA 版本: {torch.version.cuda}")
                self.log.infoln(f"🔧 GPU 数量: {torch.cuda.device_count()}")
                for i in range(torch.cuda.device_count()):
                    gpu_name = torch.cuda.get_device_name(i)
                    gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    self.log.infoln(f"🔧 GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        except Exception as e:
            self.log.warningln(f"检查 PyTorch 环境失败: {e}")

    def _log_model_paths(self):
        """记录模型路径信息"""
        try:
            import os

            self.log.infoln("🔍 MinerU 模型路径信息:")

            # 检查 PyTorch 环境
            self._check_torch_environment()

            # 检查环境变量
            use_local_model = settings.MINERU_USE_LOCAL_MODEL
            self.log.infoln(f"使用本地模型: {use_local_model}")

            # 如果启用本地模型，显示本地模型路径信息
            if use_local_model:
                if settings.MINERU_LOCAL_MODEL_PATH:
                    self.log.infoln(f"本地模型根目录: {settings.MINERU_LOCAL_MODEL_PATH}")

                    # 检查各个模型文件是否存在
                    model_files = [
                        ("布局检测模型", "models/Layout/YOLO/doclayout_yolo_docstructbench_imgsz1280_2501.pt"),
                        ("OCR模型", "models/OCR/paddleocr_torch"),
                        ("公式检测模型", "models/MFD/YOLO/yolo_v8_ft.pt"),
                        ("公式识别模型", "models/MFR/unimernet_hf_small_2503"),
                        ("表格识别模型", "models/TabRec/SlanetPlus/slanet-plus.onnx"),
                    ]

                    for model_name, relative_path in model_files:
                        try:
                            full_path = os.path.join(settings.MINERU_LOCAL_MODEL_PATH, relative_path)

                            if os.path.exists(full_path):
                                if os.path.isfile(full_path):
                                    file_size = os.path.getsize(full_path) / (1024 * 1024)  # 文件大小(MB)
                                    self.log.infoln(f"{model_name}: ✅ 本地文件 ({file_size:.1f} MB)")
                                else:
                                    self.log.infoln(f"{model_name}: ✅ 本地目录")
                                self.log.infoln(f"路径: {full_path}")
                            else:
                                self.log.warningln(f"{model_name}: ❌ 文件不存在")
                                self.log.warningln(f"路径: {full_path}")

                        except Exception as e:
                            self.log.warningln(f"{model_name}: 检查失败 - {e}")
                else:
                    self.log.warningln("⚠️  本地模型路径未配置")
            else:
                # 使用 MinerU 内置智能下载机制
                self.log.infoln("📦 使用 MinerU 内置智能下载机制")
                self.log.infoln("🔄 模型将在使用时自动下载和缓存")

        except Exception as e:
            self.log.errorln(f"记录模型路径失败: {e}")


    def analyze_question_content_ocr(
            self,
            image_bytes: bytes,
            enable_formula_recognition: bool = True,
            table_enable: bool = True
        ) -> Optional[str]:
            """
            对图像执行OCR，以提取文本信息。

            该方法利用MinerU引擎提取文本。

            参数:
                image_bytes: 要处理的图像的字节内容。
                enable_formula_recognition: 如果为True，则启用公式识别。

            返回:
                如果成功，则返回一个包含识别文本的字符串，否则返回None。
            """
            import time
            start_time = time.time()
            self.log.infoln(f"🚀 开始OCR处理 (公式识别: {enable_formula_recognition},表格识别：{table_enable})...")

            try:
                # 步骤1: 图像加载
                step1_start = time.time()
                image = Image.open(io.BytesIO(image_bytes))
                self.log.infoln(f"⏱️ 步骤1-图像加载: {time.time() - step1_start:.3f}s")

                # 步骤2: 图片转PDF
                step2_start = time.time()
                pdf_buffer = io.BytesIO()
                image.save(pdf_buffer, format='PDF')
                pdf_bytes = pdf_buffer.getvalue()
                self.log.infoln(f"⏱️ 步骤2-PDF转换: {time.time() - step2_start:.3f}s")

                # 步骤3: MinerU 核心分析（带重试机制）
                step3_start = time.time()

                # 重试机制处理网络连接问题
                max_retries = settings.MINERU_DOWNLOAD_RETRIES
                retry_delay = 2.0

                for attempt in range(max_retries):
                    try:
                        self.log.infoln(f"🔄 MinerU分析尝试 {attempt + 1}/{max_retries}")
                        infer_result = doc_analyze(
                            pdf_bytes_list=[pdf_bytes],
                            lang_list=['ch'],
                            parse_method='auto',
                            formula_enable=enable_formula_recognition,
                            table_enable=table_enable
                        )
                        self.log.infoln(f"⏱️ 步骤3-MinerU核心分析: {time.time() - step3_start:.3f}s")
                        break  # 成功则跳出重试循环

                    except Exception as e:
                        error_msg = str(e)
                        if "Connection" in error_msg or "reset by peer" in error_msg:
                            self.log.warningln(f"⚠️  网络连接问题 (尝试 {attempt + 1}/{max_retries}): {error_msg}")
                            if attempt < max_retries - 1:
                                self.log.infoln(f"⏳ 等待 {retry_delay}s 后重试...")
                                time.sleep(retry_delay)
                                retry_delay *= 1.5  # 指数退避
                                continue
                        # 非网络错误或最后一次尝试失败，重新抛出异常
                        raise

                if not infer_result or not infer_result[0]:
                    self.log.warningln("MinerU doc_analyze 返回了一个空的或无效的结果。")
                    return None

                # 步骤4: 结果提取和处理
                step4_start = time.time()
                ocr_result = self._extract_ocr_result_from_mineru(infer_result, image)
                self.log.infoln(f"⏱️ 步骤4-结果提取: {time.time() - step4_start:.3f}s")
                
                if ocr_result:
                    self.log.infoln(f"✅ OCR处理成功完成，耗时 {time.time() - start_time:.3f}s. "
                                f"提取了 {len(ocr_result.blocks)} 个块。")
                    return ocr_result.full_text
                else:
                    self.log.warningln("未能从MinerU的输出中提取有效的OCR结果。")
                    return None

            except Exception as e:
                self.log.errorln(f"OCR处理失败: {e}\n{traceback.format_exc()}")
                return None
        


    def _extract_ocr_result_from_mineru(self, infer_result, image: Image.Image) -> Optional[OcrResult]:
        """从MinerU推断结果中提取结构化的OCR数据。"""
        if not (isinstance(infer_result, tuple) and len(infer_result) > 0 and
                isinstance(infer_result[0], list) and len(infer_result[0]) > 0 and
                isinstance(infer_result[0][0], list) and len(infer_result[0][0]) > 0 and
                isinstance(infer_result[0][0][0], dict)):
            self.log.warningln("MinerU结果结构不符合预期。")
            return None

        page_info = infer_result[0][0][0]
        layout_dets = page_info.get('layout_dets', [])
        if not layout_dets:
            self.log.infoln("在MinerU结果中未找到布局检测结果。")
            return None

        blocks: List[TextBlock] = []
        text_parts: List[str] = []

        # 按垂直位置对检测结果进行排序，以保持阅读顺序
        sorted_dets = sorted(
            (det for det in layout_dets if isinstance(det, dict) and 'poly' in det and len(det['poly']) >= 2),
            key=lambda det: det['poly'][1]
        )

        for det in sorted_dets:
            text = det.get('text', '')
            is_formula = False
            if not text:
                latex = det.get('latex', '')
                if latex:
                    text = latex  # 对于公式，保持latex格式
                    is_formula = True

            if text and text.strip():
                clean_text = text.strip()
                text_parts.append(clean_text)

                poly = det.get('poly', [])
                if len(poly) >= 8:
                    bbox = [poly[0], poly[1], poly[4], poly[5]]
                elif len(poly) >= 4:
                    bbox = poly[:4]
                else:
                    continue  # 如果没有有效的边界框，则跳过

                blocks.append(TextBlock(
                    text=clean_text,
                    bbox=bbox,
                    score=det.get('score', 0.0),
                    type='formula' if is_formula else det.get('category', 'text')
                ))

        if not blocks:
            self.log.infoln("无法从布局检测中提取文本块。")
            return None

        page_info_data = page_info.get('page_info', {})

        # 处理 numpy 数组和 PIL Image 的尺寸获取
        if isinstance(image, np.ndarray):
            # numpy 数组格式: (height, width, channels)
            default_height, default_width = image.shape[:2]
        else:
            # PIL Image 格式
            default_width, default_height = image.width, image.height

        page_width = page_info_data.get('width', default_width)
        page_height = page_info_data.get('height', default_height)

        return OcrResult(
            full_text=" ".join(text_parts),
            blocks=blocks,
            page_width=int(page_width),
            page_height=int(page_height)
        )

