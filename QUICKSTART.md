# MinerU Servic 快速启动指南

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 下载 MinerU 模型

```bash
# 下载所有模型（推荐）
mineru-models-download -s huggingface -m all

# 或者只下载必要模型
mineru-models-download -s huggingface -m layout
mineru-models-download -s huggingface -m ocr
mineru-models-download -s huggingface -m mfd
mineru-models-download -s huggingface -m mfr
```

### 3. 配置环境变量（可选）

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 4. 启动服务

```bash
# 方式1：使用启动脚本（推荐）
./scripts/start.sh

# 方式2：直接启动
python -m service.main

# 方式3：使用 uvicorn
uvicorn service.main:app --host 0.0.0.0 --port 8000
```

### 5. 测试服务

```bash
# 健康检查
curl http://localhost:8000/health

# API 测试
python scripts/test_api.py

# 或者访问 API 文档
# http://localhost:8000/docs
```

## 📡 API 使用示例

### 健康检查

```bash
curl -X GET "http://localhost:8000/health"
```

### 问题分析

```bash
curl -X POST "http://localhost:8000/question_analyze" \
  -H "Content-Type: multipart/form-data" \
  -F "image_files=@test_image.png" \
  -F "subject=math" \
  -F "option=7"
```

### Python 客户端示例

```python
import requests

# 健康检查
response = requests.get("http://localhost:8000/health")
print(response.json())

# 问题分析
with open("test_image.png", "rb") as f:
    files = {"image_files": f}
    data = {
        "subject": "math",
        "option": 7  # 7=执行OCR+区域分析，6=仅区域分析，1/2/4=不执行分析
    }
    response = requests.post(
        "http://localhost:8000/question_analyze",
        files=files,
        data=data
    )
    print(response.json())
```

## 🐳 Docker 部署

### 构建镜像

```bash
docker build -t mineru-service-2 .
```

### 运行容器

```bash
docker run -d \
  --name mineru-service-2 \
  --gpus all \
  -p 8000:8000 \
  -v $(pwd)/models:/app/models \
  -v $(pwd)/cache:/app/cache \
  mineru-service-2
```

### 使用 Docker Compose

```bash
docker-compose up -d
```

## ⚙️ 性能优化

### 环境变量优化

```bash
# 质量优化
export MINERU_OCR_CONFIDENCE_THRESHOLD=0.7
export MINERU_MFD_CONFIDENCE_THRESHOLD=0.4
export MINERU_MFR_CONFIDENCE_THRESHOLD=0.7

# 性能优化
export MINERU_MIN_BATCH_INFERENCE_SIZE=150
export MINERU_OCR_BATCH_SIZE=10
export MINERU_MFR_BATCH_SIZE=6

# GPU 配置
export MINERU_DEVICE=cuda
export MINERU_VRAM=8
```

### 硬件要求

- **CPU**: 4核以上
- **内存**: 8GB 以上
- **GPU**: NVIDIA GPU with 8GB+ VRAM（推荐）
- **存储**: 10GB 以上（用于模型存储）

## 🔧 故障排除

### 常见问题

1. **MinerU 导入失败**
   ```bash
   pip install mineru==2.0.6
   ```

2. **CUDA 不可用**
   ```bash
   # 检查 CUDA 版本
   nvidia-smi
   
   # 安装对应的 PyTorch
   pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
   ```

3. **模型下载失败**
   ```bash
   # 使用镜像源
   export HF_ENDPOINT=https://hf-mirror.com
   mineru-models-download -s huggingface -m all
   ```

4. **内存不足**
   ```bash
   # 减少批处理大小
   export MINERU_OCR_BATCH_SIZE=4
   export MINERU_MFR_BATCH_SIZE=2
   ```

### 日志查看

```bash
# 查看服务日志
tail -f logs/mineru-service.log

# 启用调试模式
export MINERU_SERVICE_DEBUG=true
export MINERU_SERVICE_LOG_LEVEL=DEBUG
```

## 📊 监控和维护

### 健康检查

服务提供了健康检查端点，可以用于监控：

```bash
# 简单检查
curl http://localhost:8000/health

# 详细检查（包含 MinerU 版本信息）
curl -s http://localhost:8000/health | jq .
```

### 性能监控

建议监控以下指标：
- 响应时间
- 内存使用率
- GPU 使用率
- 错误率

## 🔗 相关链接

- [MinerU 官方文档](https://github.com/opendatalab/MinerU)
- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [API 文档](http://localhost:8000/docs)（服务启动后访问）
